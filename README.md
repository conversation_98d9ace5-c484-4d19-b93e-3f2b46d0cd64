# Surge Rules Collector

一个用于收集和管理Surge分流规则的Python脚本。

## 功能特性

- 🚀 从多个知名规则源自动收集规则
- 📝 验证规则格式的有效性
- 📁 自动分类保存规则文件
- 📊 生成收集摘要报告
- 🔧 支持自定义规则源
- 🌐 支持从单个URL收集规则

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 使用默认规则源

```bash
python surge_rules_collector.py --default
```

这将从内置的默认规则源（Loyalsoldier、blackmatrix7等）收集规则。

### 2. 从单个URL收集规则

```bash
python surge_rules_collector.py --url "https://example.com/rules.list" --name "my_custom_rules"
```

### 3. 使用自定义规则源文件

```bash
python surge_rules_collector.py --sources sources_example.json
```

### 4. 指定输出目录

```bash
python surge_rules_collector.py --default --output /path/to/output
```

## 命令行参数

- `-o, --output`: 指定输出目录（默认：rules）
- `-u, --url`: 从指定URL收集规则
- `-n, --name`: 自定义规则名称（与--url一起使用）
- `--default`: 使用默认规则源
- `--sources`: 指定自定义规则源JSON文件路径

## 输出文件结构

```
rules/
├── Loyalsoldier_direct.list
├── Loyalsoldier_proxy.list
├── Loyalsoldier_reject.list
├── blackmatrix7_China.list
├── blackmatrix7_Global.list
├── ...
└── collection_summary.json
```

## 支持的规则类型

脚本支持以下Surge规则类型：

- `DOMAIN` - 域名匹配
- `DOMAIN-SUFFIX` - 域名后缀匹配
- `DOMAIN-KEYWORD` - 域名关键词匹配
- `IP-CIDR` - IPv4 CIDR匹配
- `IP-CIDR6` - IPv6 CIDR匹配
- `USER-AGENT` - User-Agent匹配
- `URL-REGEX` - URL正则匹配
- `PROCESS-NAME` - 进程名匹配
- `AND/OR/NOT` - 逻辑组合规则
- `SUBNET` - 子网匹配
- `DEST-PORT/SRC-PORT/IN-PORT` - 端口匹配
- `PROTOCOL` - 协议匹配
- `SCRIPT` - 脚本规则

## 默认规则源

脚本内置了以下知名规则源：

### Loyalsoldier
- 直连规则 (direct)
- 代理规则 (proxy)
- 拒绝规则 (reject)
- Apple服务规则 (apple)
- Google服务规则 (google)
- GFW规则 (gfw)

### blackmatrix7
- 中国大陆规则 (China)
- 全球代理规则 (Global)
- Apple服务规则 (Apple)
- Google服务规则 (Google)
- Microsoft服务规则 (Microsoft)
- 广告拦截规则 (Advertising)

## 自定义规则源

您可以创建自己的规则源JSON文件，格式如下：

```json
{
  "source_name": {
    "rule_type": "https://example.com/rule.list",
    "another_rule": "https://example.com/another.list"
  },
  "another_source": {
    "rule_type": "https://another-source.com/rule.list"
  }
}
```

## 示例用法

### 快速开始
```bash
# 使用默认源收集所有规则
python surge_rules_collector.py --default

# 查看收集结果
ls rules/
cat rules/collection_summary.json
```

### 收集特定规则
```bash
# 从GitHub收集自定义规则
python surge_rules_collector.py \
  --url "https://raw.githubusercontent.com/user/repo/main/rules.list" \
  --name "github_custom"
```

### 批量收集
```bash
# 使用自定义源文件批量收集
python surge_rules_collector.py --sources my_sources.json --output my_rules
```

## 注意事项

1. 网络连接：确保网络连接正常，能够访问规则源
2. 文件权限：确保脚本有权限在指定目录创建文件
3. 规则验证：脚本会自动验证规则格式，无效规则将被过滤
4. 更新频率：建议定期运行脚本以获取最新规则

## 故障排除

### 下载失败
- 检查网络连接
- 确认规则源URL是否有效
- 尝试使用代理

### 权限错误
- 确保对输出目录有写权限
- 使用绝对路径指定输出目录

### 规则格式错误
- 脚本会自动过滤无效规则
- 检查规则源是否为Surge格式

## 许可证

MIT License
