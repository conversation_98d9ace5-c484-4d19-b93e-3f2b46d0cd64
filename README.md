# Surge Rules Collector - 四类分流规则收集器

专门收集和整理 Surge 分流规则的 Python 脚本，自动分类为四个核心类别。

## 🎯 四类规则

1. **国内网站** - 中国大陆网站域名规则，建议策略：DIRECT
2. **国内 IP** - 中国大陆 IP 段和 ASN 规则，建议策略：DIRECT
3. **国外常见网站** - 国外主流网站和服务，建议策略：PROXY
4. **AI 类网站** - AI 相关网站和服务，建议策略：PROXY

## ✨ 功能特性

- 🚀 自动从多个知名规则源收集规则
- 🔍 智能分类规则到四个核心类别
- 📝 验证规则格式有效性（支持 IP-ASN 规则）
- 📁 按类别自动保存规则文件
- 📊 生成详细的收集摘要报告
- 📄 自动生成 Surge 配置模板
- 🎨 去重和排序优化

## 📦 安装依赖

```bash
pip install requests
```

## 🚀 快速使用

### 基本用法（推荐）

```bash
# 收集所有四类规则
python surge_rules_collector.py
```

### 指定输出目录

```bash
python surge_rules_collector.py -o my_surge_rules
```

## 📋 命令行参数

- `-o, --output`: 指定输出目录（默认：surge_rules）
- `-h, --help`: 显示帮助信息

## 📁 输出文件结构

```text
surge_rules/
├── 01_domestic_websites.list      # 国内网站规则
├── 02_domestic_ip.list           # 国内IP规则
├── 03_foreign_websites.list      # 国外网站规则
├── 04_ai_websites.list          # AI类网站规则
├── collection_summary.json      # 收集摘要
└── surge_config_template.conf   # Surge配置模板
```

## 🔧 支持的规则类型

脚本支持所有标准 Surge 规则类型：

- `DOMAIN` - 域名完全匹配
- `DOMAIN-SUFFIX` - 域名后缀匹配
- `DOMAIN-KEYWORD` - 域名关键词匹配
- `IP-CIDR` - IPv4 CIDR 匹配
- `IP-CIDR6` - IPv6 CIDR 匹配
- `IP-ASN` - ASN 匹配（重点支持）
- `USER-AGENT` - User-Agent 匹配
- `URL-REGEX` - URL 正则匹配
- `PROCESS-NAME` - 进程名匹配
- 逻辑组合规则 (`AND/OR/NOT`)
- 端口匹配规则 (`DEST-PORT/SRC-PORT/IN-PORT`)
- `PROTOCOL` - 协议匹配
- `SCRIPT` - 脚本规则

## 📡 规则源

### 国内网站规则源

- **Loyalsoldier** - 直连规则
- **blackmatrix7** - 中国大陆规则
- **ACL4SSR** - 中国域名规则

### 国内 IP 规则源

- **Loyalsoldier** - 中国 CIDR 规则
- **blackmatrix7** - 中国 IP 段规则
- **blackmatrix7** - 中国 ASN 规则

### 国外网站规则源

- **Loyalsoldier** - 代理规则、GFW 规则
- **blackmatrix7** - 全球规则、各大平台规则
  - Google、YouTube、Netflix
  - Twitter、Facebook、Telegram

### AI 网站规则源

- **blackmatrix7** - AI 平台规则
  - OpenAI、Claude、Copilot、Gemini
- **自定义规则** - 补充 AI 相关域名

## 📊 运行示例

```bash
$ python surge_rules_collector.py

🚀 开始收集Surge分流规则...
============================================================

📂 正在收集 domestic_websites 类规则...
正在下载: https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt
loyalsoldier_direct: 找到 2847 条有效规则

💾 正在保存分类规则...
============================================================
✅ 国内网站直连规则: 15234 条规则已保存
✅ 国内IP直连规则: 8734 条规则已保存
✅ 国外常见网站代理规则: 12456 条规则已保存
✅ AI类网站和服务规则: 234 条规则已保存

🎉 规则收集完成!
📊 总规则数: 36658
```

## 🎯 使用建议

### 1. 在 Surge 中使用规则

将生成的规则文件上传到 GitHub，然后在 Surge 配置中引用：

```ini
[Rule]
# AI类网站 (最高优先级)
RULE-SET,https://your-repo/04_ai_websites.list,AI

# 国外网站
RULE-SET,https://your-repo/03_foreign_websites.list,PROXY

# 国内网站和IP
RULE-SET,https://your-repo/01_domestic_websites.list,DIRECT
RULE-SET,https://your-repo/02_domestic_ip.list,DIRECT
```

### 2. 定期更新规则

```bash
# 每天凌晨2点更新规则
0 2 * * * cd /path/to/surge_rules && python surge_rules_collector.py
```

## ⚠️ 注意事项

1. **网络连接** - 确保能够访问 GitHub 等规则源
2. **文件权限** - 确保脚本有权限在指定目录创建文件
3. **规则验证** - 脚本会自动验证和过滤无效规则
4. **去重处理** - 自动去除重复规则并排序
5. **更新频率** - 建议每天或每周运行一次

## 🔧 故障排除

### 下载失败

```bash
# 检查网络连接
curl -I https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt

# 使用代理
export https_proxy=http://127.0.0.1:7890
python surge_rules_collector.py
```

### 权限错误

```bash
# 确保目录权限
chmod 755 surge_rules/
```

## 📄 许可证

MIT License - 自由使用和修改
