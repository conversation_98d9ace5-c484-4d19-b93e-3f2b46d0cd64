#!/usr/bin/env python3
"""
Surge Rules Collector 使用示例
"""

import os
import sys
from surge_rules_collector import SurgeRulesCollector


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建收集器实例
    collector = SurgeRulesCollector("example_rules")
    
    # 使用默认规则源收集
    print("正在从默认源收集规则...")
    all_rules = collector.collect_from_sources(collector.default_sources)
    
    # 生成摘要
    collector.generate_summary(all_rules)
    
    print("基本示例完成！")


def example_custom_source():
    """自定义源示例"""
    print("\n=== 自定义源示例 ===")
    
    collector = SurgeRulesCollector("custom_rules")
    
    # 自定义规则源
    custom_sources = {
        "MyCustom": {
            "ad_block": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Advertising/Advertising.list",
            "china_direct": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt"
        }
    }
    
    print("正在从自定义源收集规则...")
    all_rules = collector.collect_from_sources(custom_sources)
    collector.generate_summary(all_rules)
    
    print("自定义源示例完成！")


def example_single_url():
    """单个URL示例"""
    print("\n=== 单个URL示例 ===")
    
    collector = SurgeRulesCollector("single_url_rules")
    
    # 从单个URL收集
    url = "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/reject.txt"
    rules = collector.collect_from_url(url, "reject_ads")
    
    if rules:
        print(f"成功收集了 {len(rules)} 条规则")
    
    print("单个URL示例完成！")


def show_collected_files():
    """显示收集到的文件"""
    print("\n=== 收集结果 ===")
    
    directories = ["example_rules", "custom_rules", "single_url_rules"]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"\n{directory}/ 目录内容:")
            files = os.listdir(directory)
            for file in sorted(files):
                filepath = os.path.join(directory, file)
                if os.path.isfile(filepath):
                    size = os.path.getsize(filepath)
                    print(f"  {file} ({size} bytes)")


def main():
    """主函数"""
    print("Surge Rules Collector 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_custom_source()
        example_single_url()
        
        # 显示结果
        show_collected_files()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        print("您可以查看生成的规则文件和摘要。")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
