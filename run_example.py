#!/usr/bin/env python3
"""
Surge Rules Collector 使用示例 - 四类分流规则
"""

import os
import sys
from surge_rules_collector import SurgeRulesCollector


def example_basic_usage():
    """基本使用示例 - 收集四类规则"""
    print("=== 基本使用示例：收集四类分流规则 ===")

    # 创建收集器实例
    collector = SurgeRulesCollector("example_rules")

    # 收集所有四类规则
    print("正在收集四类分流规则...")
    all_rules = collector.collect_all_rules()

    # 保存分类规则
    category_summary, total_rules = collector.save_all_categories(all_rules)

    # 生成摘要
    collector.generate_summary(category_summary, total_rules)

    # 创建配置模板
    collector.create_surge_config_template()

    print("基本示例完成！")
    return category_summary, total_rules


def example_custom_output_dir():
    """自定义输出目录示例"""
    print("\n=== 自定义输出目录示例 ===")

    collector = SurgeRulesCollector("my_custom_rules")

    print("正在收集规则到自定义目录...")
    all_rules = collector.collect_all_rules()
    category_summary, total_rules = collector.save_all_categories(all_rules)
    collector.generate_summary(category_summary, total_rules)

    print("自定义目录示例完成！")
    return category_summary, total_rules


def show_collected_files():
    """显示收集到的文件"""
    print("\n=== 收集结果展示 ===")

    directories = ["example_rules", "my_custom_rules"]

    for directory in directories:
        if os.path.exists(directory):
            print(f"\n📁 {directory}/ 目录内容:")
            files = os.listdir(directory)
            for file in sorted(files):
                filepath = os.path.join(directory, file)
                if os.path.isfile(filepath):
                    size = os.path.getsize(filepath)
                    if file.endswith('.list'):
                        # 统计规则数量
                        with open(filepath, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            rule_count = sum(1 for line in lines if line.strip() and not line.startswith('#'))
                        print(f"  📄 {file} ({size} bytes, {rule_count} 条规则)")
                    else:
                        print(f"  📄 {file} ({size} bytes)")


def show_rule_samples():
    """显示规则样本"""
    print("\n=== 规则样本展示 ===")

    sample_files = [
        ("example_rules/01_domestic_websites.list", "国内网站规则"),
        ("example_rules/02_domestic_ip.list", "国内IP规则"),
        ("example_rules/03_foreign_websites.list", "国外网站规则"),
        ("example_rules/04_ai_websites.list", "AI网站规则")
    ]

    for filepath, description in sample_files:
        if os.path.exists(filepath):
            print(f"\n📋 {description} 样本:")
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                rule_lines = [line.strip() for line in lines if line.strip() and not line.startswith('#')]

                # 显示前5条规则
                for i, rule in enumerate(rule_lines[:5]):
                    print(f"  {i+1}. {rule}")

                if len(rule_lines) > 5:
                    print(f"  ... 还有 {len(rule_lines) - 5} 条规则")


def main():
    """主函数"""
    print("🚀 Surge Rules Collector - 四类分流规则收集示例")
    print("=" * 60)

    try:
        # 运行基本示例
        summary1, total1 = example_basic_usage()

        # 运行自定义目录示例
        summary2, total2 = example_custom_output_dir()

        # 显示收集结果
        show_collected_files()

        # 显示规则样本
        show_rule_samples()

        # 显示最终统计
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("=" * 60)
        print(f"📊 示例1总规则数: {total1}")
        print(f"📊 示例2总规则数: {total2}")

        print("\n💡 使用建议:")
        print("1. 将生成的规则文件上传到GitHub等托管服务")
        print("2. 在Surge配置中使用RULE-SET引用规则文件")
        print("3. 参考生成的surge_config_template.conf配置模板")
        print("4. 定期运行脚本以获取最新规则")

    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
