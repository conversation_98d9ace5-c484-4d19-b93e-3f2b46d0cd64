{"description": "Surge分流规则源配置 - 四类规则专用", "categories": {"domestic_websites": {"description": "国内网站规则源", "sources": {"loyalsoldier_direct": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt", "blackmatrix7_china": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/China/China.list", "acl4ssr_china": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list"}}, "domestic_ip": {"description": "国内IP规则源（包含ASN）", "sources": {"loyalsoldier_cncidr": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/cncidr.txt", "blackmatrix7_chinaips": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/ChinaIPs/ChinaIPs.list", "china_asn": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/ChinaASN/ChinaASN.list"}}, "foreign_websites": {"description": "国外常见网站规则源", "sources": {"loyalsoldier_proxy": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/proxy.txt", "loyalsoldier_gfw": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/gfw.txt", "blackmatrix7_global": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Global/Global.list", "blackmatrix7_google": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Google/Google.list", "blackmatrix7_youtube": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTube/YouTube.list", "blackmatrix7_netflix": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Netflix/Netflix.list", "blackmatrix7_twitter": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Twitter/Twitter.list", "blackmatrix7_facebook": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Facebook/Facebook.list"}}, "ai_websites": {"description": "AI类网站和服务规则源", "sources": {"blackmatrix7_openai": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/OpenAI/OpenAI.list", "blackmatrix7_claude": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Claude/Claude.list", "blackmatrix7_copilot": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Copilot/Copilot.list", "blackmatrix7_gemini": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Gemini/Gemini.list"}}}, "custom_ai_rules": ["DOMAIN-SUFFIX,openai.com", "DOMAIN-SUFFIX,chatgpt.com", "DOMAIN-SUFFIX,anthropic.com", "DOMAIN-SUFFIX,claude.ai", "DOMAIN-SUFFIX,midjourney.com", "DOMAIN-SUFFIX,stability.ai", "DOMAIN-SUFFIX,huggingface.co", "DOMAIN-SUFFIX,replicate.com", "DOMAIN-SUFFIX,cohere.ai", "DOMAIN-SUFFIX,perplexity.ai", "DOMAIN-SUFFIX,character.ai", "DOMAIN-SUFFIX,poe.com"]}