{"Loyalsoldier": {"direct": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt", "proxy": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/proxy.txt", "reject": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/reject.txt", "apple": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/apple.txt", "google": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/google.txt", "gfw": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/gfw.txt"}, "blackmatrix7": {"China": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/China/China.list", "Global": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Global/Global.list", "Apple": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Apple/Apple.list", "Google": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Google/Google.list", "Microsoft": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Microsoft/Microsoft.list", "Advertising": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Advertising/Advertising.list", "YouTube": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTube/YouTube.list", "Netflix": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Netflix/Netflix.list", "Telegram": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Telegram/Telegram.list", "Twitter": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Twitter/Twitter.list"}, "ACL4SSR": {"BanAD": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list", "LocalAreaNetwork": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/LocalAreaNetwork.list", "UnBan": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/UnBan.list", "GoogleCN": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/GoogleCN.list", "Ruleset/Apple": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Apple.list", "Ruleset/Google": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/Ruleset/Google.list"}, "lhie1": {"Reject": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Reject.list", "Special": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Special.list", "Media": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Media/Foreign.list", "Global": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Proxy.list", "Domestic": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Domestic.list"}}