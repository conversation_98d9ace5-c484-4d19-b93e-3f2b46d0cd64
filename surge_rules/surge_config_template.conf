# Surge配置模板 - 使用收集的分流规则
# Generated at: 2025-07-08 11:34:57

[General]
# 通用设置
loglevel = notify
dns-server = 223.5.5.5, 114.114.114.114, 8.8.8.8
tun-excluded-routes = 10.0.0.0/8, *********/8, 169.254.0.0/16, **********/12, ***********/16, 224.0.0.0/4, 240.0.0.0/4

[Proxy]
# 在这里添加你的代理服务器配置
# 示例: PROXY = ss, server.com, 443, encrypt-method=aes-256-gcm, password=password

[Proxy Group]
# 代理策略组
PROXY = select, DIRECT, 你的代理服务器名称
AI = select, PROXY, DIRECT

[Rule]
# 分流规则 - 按优先级排序

# 1. AI类网站和服务 (最高优先级)
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/04_ai_websites.list,AI

# 2. 国外常见网站
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/03_foreign_websites.list,PROXY

# 3. 国内网站直连
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/01_domestic_websites.list,DIRECT

# 4. 国内IP直连
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/02_domestic_ip.list,DIRECT

# 本地网络直连
DOMAIN-SUFFIX,local,DIRECT
IP-CIDR,***********/16,DIRECT
IP-CIDR,10.0.0.0/8,DIRECT
IP-CIDR,**********/12,DIRECT
IP-CIDR,*********/8,DIRECT

# 最终规则
FINAL,PROXY

[URL Rewrite]
# URL重写规则

[MITM]
# MITM设置
