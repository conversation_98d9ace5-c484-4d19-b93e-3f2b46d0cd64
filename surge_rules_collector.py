#!/usr/bin/env python3
"""
Surge Rules Collector
用于收集和管理Surge分流规则的Python脚本
"""

import os
import sys
import json
import requests
import argparse
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urlparse
import re


class SurgeRulesCollector:
    """Surge规则收集器"""
    
    def __init__(self, output_dir: str = "rules"):
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        })
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 常用的规则源
        self.default_sources = {
            "Loyalsoldier": {
                "direct": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt",
                "proxy": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/proxy.txt",
                "reject": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/reject.txt",
                "apple": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/apple.txt",
                "google": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/google.txt",
                "gfw": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/gfw.txt"
            },
            "blackmatrix7": {
                "China": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/China/China.list",
                "Global": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Global/Global.list",
                "Apple": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Apple/Apple.list",
                "Google": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Google/Google.list",
                "Microsoft": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Microsoft/Microsoft.list",
                "Advertising": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Advertising/Advertising.list"
            }
        }
    
    def download_rule(self, url: str, timeout: int = 30) -> Optional[str]:
        """下载规则文件"""
        try:
            print(f"正在下载: {url}")
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"下载失败 {url}: {e}")
            return None
    
    def validate_rule_line(self, line: str) -> bool:
        """验证规则行是否有效"""
        line = line.strip()
        if not line or line.startswith('#') or line.startswith('//'):
            return False
        
        # 检查是否是有效的Surge规则格式
        surge_rule_patterns = [
            r'^DOMAIN,',
            r'^DOMAIN-SUFFIX,',
            r'^DOMAIN-KEYWORD,',
            r'^IP-CIDR,',
            r'^IP-CIDR6,',
            r'^USER-AGENT,',
            r'^URL-REGEX,',
            r'^PROCESS-NAME,',
            r'^AND,',
            r'^OR,',
            r'^NOT,',
            r'^SUBNET,',
            r'^DEST-PORT,',
            r'^SRC-PORT,',
            r'^IN-PORT,',
            r'^PROTOCOL,',
            r'^SCRIPT,'
        ]
        
        return any(re.match(pattern, line, re.IGNORECASE) for pattern in surge_rule_patterns)
    
    def process_rules(self, content: str, rule_name: str) -> List[str]:
        """处理和清理规则内容"""
        if not content:
            return []
        
        lines = content.split('\n')
        valid_rules = []
        
        for line in lines:
            line = line.strip()
            if self.validate_rule_line(line):
                valid_rules.append(line)
        
        print(f"{rule_name}: 找到 {len(valid_rules)} 条有效规则")
        return valid_rules
    
    def save_rules(self, rules: List[str], filename: str, source_info: str = ""):
        """保存规则到文件"""
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write(f"# Surge Rules - {filename}\n")
            f.write(f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            if source_info:
                f.write(f"# Source: {source_info}\n")
            f.write(f"# Total rules: {len(rules)}\n")
            f.write("#\n\n")
            
            # 写入规则
            for rule in rules:
                f.write(f"{rule}\n")
        
        print(f"规则已保存到: {filepath}")
    
    def collect_from_sources(self, sources: Dict[str, Dict[str, str]]):
        """从指定源收集规则"""
        all_rules = {}
        
        for source_name, rule_urls in sources.items():
            print(f"\n正在处理源: {source_name}")
            
            for rule_type, url in rule_urls.items():
                content = self.download_rule(url)
                if content:
                    rules = self.process_rules(content, f"{source_name}-{rule_type}")
                    if rules:
                        key = f"{source_name}_{rule_type}"
                        all_rules[key] = {
                            'rules': rules,
                            'source': url,
                            'count': len(rules)
                        }
                        
                        # 保存单个规则文件
                        filename = f"{key}.list"
                        self.save_rules(rules, filename, url)
        
        return all_rules
    
    def collect_from_url(self, url: str, name: str):
        """从单个URL收集规则"""
        content = self.download_rule(url)
        if content:
            rules = self.process_rules(content, name)
            if rules:
                filename = f"{name}.list"
                self.save_rules(rules, filename, url)
                return rules
        return []
    
    def generate_summary(self, all_rules: Dict):
        """生成收集摘要"""
        summary_file = os.path.join(self.output_dir, "collection_summary.json")
        
        summary = {
            "generated_at": datetime.now().isoformat(),
            "total_rule_files": len(all_rules),
            "rules_summary": {}
        }
        
        total_rules = 0
        for key, data in all_rules.items():
            summary["rules_summary"][key] = {
                "count": data["count"],
                "source": data["source"]
            }
            total_rules += data["count"]
        
        summary["total_rules"] = total_rules
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n收集摘要已保存到: {summary_file}")
        print(f"总计收集了 {len(all_rules)} 个规则文件，包含 {total_rules} 条规则")


def main():
    parser = argparse.ArgumentParser(description="Surge规则收集器")
    parser.add_argument("-o", "--output", default="rules", help="输出目录 (默认: rules)")
    parser.add_argument("-u", "--url", help="从指定URL收集规则")
    parser.add_argument("-n", "--name", help="自定义规则名称 (与--url一起使用)")
    parser.add_argument("--default", action="store_true", help="使用默认规则源")
    parser.add_argument("--sources", help="自定义规则源JSON文件路径")
    
    args = parser.parse_args()
    
    collector = SurgeRulesCollector(args.output)
    
    if args.url:
        # 从单个URL收集
        name = args.name or "custom_rule"
        collector.collect_from_url(args.url, name)
    elif args.sources:
        # 从自定义源文件收集
        try:
            with open(args.sources, 'r', encoding='utf-8') as f:
                sources = json.load(f)
            all_rules = collector.collect_from_sources(sources)
            collector.generate_summary(all_rules)
        except Exception as e:
            print(f"读取源文件失败: {e}")
            sys.exit(1)
    else:
        # 使用默认源或提示用户
        if args.default:
            all_rules = collector.collect_from_sources(collector.default_sources)
            collector.generate_summary(all_rules)
        else:
            print("请指定收集选项:")
            print("  --default: 使用默认规则源")
            print("  --url URL --name NAME: 从指定URL收集")
            print("  --sources FILE: 从JSON文件指定的源收集")
            print("\n使用 -h 查看完整帮助")


if __name__ == "__main__":
    main()
