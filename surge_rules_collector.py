#!/usr/bin/env python3
"""
Surge Rules Collector - 专门收集四类分流规则
1. 国内网站
2. 国内IP（包含IP-ASN规则）
3. 国外常见网站
4. AI类网站和IP
"""

import os
import sys
import json
import requests
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Set
from urllib.parse import urlparse
import re


class SurgeRulesCollector:
    """Surge规则收集器 - 专门收集四类分流规则"""

    def __init__(self, output_dir: str = "surge_rules"):
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        })

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 四类规则的专门源
        self.rule_sources = {
            "domestic_websites": {
                "loyalsoldier_direct": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/direct.txt",
                "blackmatrix7_china": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/China/China.list",
                "acl4ssr_china": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list",
                "lhie1_domestic": "https://raw.githubusercontent.com/lhie1/Rules/master/Surge/Surge%203/Provider/Domestic.list"
            },
            "domestic_ip": {
                "loyalsoldier_cncidr": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/cncidr.txt",
                "blackmatrix7_chinaips": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/ChinaIPs/ChinaIPs.list",
                "acl4ssr_chinaip": "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaIp.list",
                "china_asn": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/ChinaASN/ChinaASN.list"
            },
            "foreign_websites": {
                "loyalsoldier_proxy": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/proxy.txt",
                "loyalsoldier_gfw": "https://raw.githubusercontent.com/Loyalsoldier/surge-rules/release/gfw.txt",
                "blackmatrix7_global": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Global/Global.list",
                "blackmatrix7_google": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Google/Google.list",
                "blackmatrix7_youtube": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/YouTube/YouTube.list",
                "blackmatrix7_netflix": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Netflix/Netflix.list",
                "blackmatrix7_twitter": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Twitter/Twitter.list",
                "blackmatrix7_facebook": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Facebook/Facebook.list",
                "blackmatrix7_telegram": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Telegram/Telegram.list"
            },
            "ai_websites": {
                "openai": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/OpenAI/OpenAI.list",
                "claude": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Claude/Claude.list",
                "copilot": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Copilot/Copilot.list",
                "gemini": "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Surge/Gemini/Gemini.list"
            }
        }

        # AI相关的自定义规则
        self.ai_custom_rules = [
            "DOMAIN-SUFFIX,openai.com",
            "DOMAIN-SUFFIX,chatgpt.com",
            "DOMAIN-SUFFIX,anthropic.com",
            "DOMAIN-SUFFIX,claude.ai",
            "DOMAIN-SUFFIX,midjourney.com",
            "DOMAIN-SUFFIX,stability.ai",
            "DOMAIN-SUFFIX,huggingface.co",
            "DOMAIN-SUFFIX,replicate.com",
            "DOMAIN-SUFFIX,cohere.ai",
            "DOMAIN-SUFFIX,perplexity.ai",
            "DOMAIN-SUFFIX,character.ai",
            "DOMAIN-SUFFIX,poe.com",
            "DOMAIN-SUFFIX,bard.google.com",
            "DOMAIN-SUFFIX,gemini.google.com",
            "DOMAIN-SUFFIX,copilot.microsoft.com",
            "DOMAIN-SUFFIX,github.com/features/copilot",
            "DOMAIN-KEYWORD,chatgpt",
            "DOMAIN-KEYWORD,openai",
            "DOMAIN-KEYWORD,claude",
            "DOMAIN-KEYWORD,gemini"
        ]

    def download_rule(self, url: str, timeout: int = 30) -> Optional[str]:
        """下载规则文件"""
        try:
            print(f"正在下载: {url}")
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            print(f"下载失败 {url}: {e}")
            return None

    def validate_rule_line(self, line: str) -> bool:
        """验证规则行是否有效"""
        line = line.strip()
        if not line or line.startswith('#') or line.startswith('//'):
            return False

        # 检查是否是有效的Surge规则格式（包括IP-ASN）
        surge_rule_patterns = [
            r'^DOMAIN,',
            r'^DOMAIN-SUFFIX,',
            r'^DOMAIN-KEYWORD,',
            r'^IP-CIDR,',
            r'^IP-CIDR6,',
            r'^IP-ASN,',  # 添加IP-ASN支持
            r'^USER-AGENT,',
            r'^URL-REGEX,',
            r'^PROCESS-NAME,',
            r'^AND,',
            r'^OR,',
            r'^NOT,',
            r'^SUBNET,',
            r'^DEST-PORT,',
            r'^SRC-PORT,',
            r'^IN-PORT,',
            r'^PROTOCOL,',
            r'^SCRIPT,'
        ]

        return any(re.match(pattern, line, re.IGNORECASE) for pattern in surge_rule_patterns)

    def categorize_rule(self, rule: str) -> str:
        """根据规则内容判断类别"""
        rule_lower = rule.lower()

        # AI相关关键词
        ai_keywords = ['openai', 'chatgpt', 'claude', 'anthropic', 'gemini', 'bard',
                      'copilot', 'midjourney', 'stability', 'huggingface', 'replicate',
                      'cohere', 'perplexity', 'character.ai', 'poe.com']

        if any(keyword in rule_lower for keyword in ai_keywords):
            return 'ai'

        # 国内相关关键词
        domestic_keywords = ['baidu', 'tencent', 'alibaba', 'taobao', 'qq', 'weixin',
                           'sina', 'sohu', 'netease', '163', 'douban', 'zhihu',
                           'bilibili', 'iqiyi', 'youku', 'alipay', 'jd.com']

        if any(keyword in rule_lower for keyword in domestic_keywords):
            return 'domestic'

        # IP规则
        if rule.startswith(('IP-CIDR,', 'IP-CIDR6,', 'IP-ASN,')):
            return 'ip'

        # 默认为国外网站
        return 'foreign'

    def process_rules_by_category(self, content: str, rule_name: str) -> Dict[str, List[str]]:
        """处理规则并按类别分类"""
        if not content:
            return {}

        lines = content.split('\n')
        categorized_rules = {
            'domestic': [],
            'ip': [],
            'foreign': [],
            'ai': []
        }

        for line in lines:
            line = line.strip()
            if self.validate_rule_line(line):
                category = self.categorize_rule(line)
                if category == 'domestic':
                    categorized_rules['domestic'].append(line)
                elif category == 'ip':
                    categorized_rules['ip'].append(line)
                elif category == 'ai':
                    categorized_rules['ai'].append(line)
                else:
                    categorized_rules['foreign'].append(line)

        total_rules = sum(len(rules) for rules in categorized_rules.values())
        print(f"{rule_name}: 找到 {total_rules} 条有效规则")
        for category, rules in categorized_rules.items():
            if rules:
                print(f"  - {category}: {len(rules)} 条")

        return categorized_rules

    def save_category_rules(self, rules: List[str], category: str, description: str):
        """保存分类规则到文件"""
        filename_map = {
            'domestic': '01_domestic_websites.list',
            'ip': '02_domestic_ip.list',
            'foreign': '03_foreign_websites.list',
            'ai': '04_ai_websites.list'
        }

        filepath = os.path.join(self.output_dir, filename_map[category])

        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write(f"# Surge Rules - {description}\n")
            f.write(f"# Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Category: {category}\n")
            f.write(f"# Total rules: {len(rules)}\n")
            f.write("#\n")
            f.write("# 使用说明:\n")
            if category == 'domestic':
                f.write("# 国内网站直连规则，建议策略: DIRECT\n")
            elif category == 'ip':
                f.write("# 国内IP直连规则（包含IP-ASN），建议策略: DIRECT\n")
            elif category == 'foreign':
                f.write("# 国外常见网站代理规则，建议策略: PROXY\n")
            elif category == 'ai':
                f.write("# AI类网站和服务规则，建议策略: PROXY\n")
            f.write("#\n\n")

            # 去重并排序
            unique_rules = sorted(list(set(rules)))

            # 写入规则
            for rule in unique_rules:
                f.write(f"{rule}\n")

        print(f"✅ {description}: {len(unique_rules)} 条规则已保存到 {filepath}")
        return len(unique_rules)

    def collect_all_rules(self):
        """收集所有四类规则"""
        print("🚀 开始收集Surge分流规则...")
        print("=" * 60)

        # 存储所有分类的规则
        all_categorized_rules = {
            'domestic': [],
            'ip': [],
            'foreign': [],
            'ai': []
        }

        # 收集各类规则
        for category, sources in self.rule_sources.items():
            print(f"\n📂 正在收集 {category} 类规则...")

            for source_name, url in sources.items():
                content = self.download_rule(url)
                if content:
                    if category == 'domestic_websites':
                        categorized = self.process_rules_by_category(content, source_name)
                        all_categorized_rules['domestic'].extend(categorized.get('domestic', []))
                        all_categorized_rules['foreign'].extend(categorized.get('foreign', []))
                    elif category == 'domestic_ip':
                        categorized = self.process_rules_by_category(content, source_name)
                        all_categorized_rules['ip'].extend(categorized.get('ip', []))
                    elif category == 'foreign_websites':
                        categorized = self.process_rules_by_category(content, source_name)
                        all_categorized_rules['foreign'].extend(categorized.get('foreign', []))
                        all_categorized_rules['ai'].extend(categorized.get('ai', []))
                    elif category == 'ai_websites':
                        categorized = self.process_rules_by_category(content, source_name)
                        all_categorized_rules['ai'].extend(categorized.get('ai', []))

        # 添加自定义AI规则
        print(f"\n📝 添加自定义AI规则...")
        all_categorized_rules['ai'].extend(self.ai_custom_rules)

        return all_categorized_rules

    def save_all_categories(self, all_rules: Dict[str, List[str]]):
        """保存所有分类的规则"""
        print(f"\n💾 正在保存分类规则...")
        print("=" * 60)

        category_descriptions = {
            'domestic': '国内网站直连规则',
            'ip': '国内IP直连规则',
            'foreign': '国外常见网站代理规则',
            'ai': 'AI类网站和服务规则'
        }

        summary = {}
        total_rules = 0

        for category, rules in all_rules.items():
            if rules:
                description = category_descriptions[category]
                count = self.save_category_rules(rules, category, description)
                summary[category] = {
                    'description': description,
                    'count': count,
                    'filename': f"0{list(category_descriptions.keys()).index(category) + 1}_{category}_{'websites' if category in ['domestic', 'foreign', 'ai'] else 'ip'}.list"
                }
                total_rules += count

        return summary, total_rules

    def generate_summary(self, category_summary: Dict, total_rules: int):
        """生成收集摘要"""
        summary_file = os.path.join(self.output_dir, "collection_summary.json")

        summary = {
            "generated_at": datetime.now().isoformat(),
            "description": "Surge分流规则收集摘要 - 四类规则",
            "categories": category_summary,
            "total_rules": total_rules,
            "files_generated": len(category_summary),
            "usage_guide": {
                "domestic_websites": "国内网站直连规则 - 建议策略: DIRECT",
                "domestic_ip": "国内IP直连规则 - 建议策略: DIRECT",
                "foreign_websites": "国外常见网站代理规则 - 建议策略: PROXY",
                "ai_websites": "AI类网站和服务规则 - 建议策略: PROXY"
            }
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"\n📋 收集摘要已保存到: {summary_file}")
        return summary_file

    def create_surge_config_template(self):
        """创建Surge配置模板"""
        template_file = os.path.join(self.output_dir, "surge_config_template.conf")

        template_content = """# Surge配置模板 - 使用收集的分流规则
# Generated at: {timestamp}

[General]
# 通用设置
loglevel = notify
dns-server = *********, ***************, *******
tun-excluded-routes = 10.0.0.0/8, *********/8, ***********/16, **********/12, ***********/16, *********/4, 240.0.0.0/4

[Proxy]
# 在这里添加你的代理服务器配置
# 示例: PROXY = ss, server.com, 443, encrypt-method=aes-256-gcm, password=password

[Proxy Group]
# 代理策略组
PROXY = select, DIRECT, 你的代理服务器名称
AI = select, PROXY, DIRECT

[Rule]
# 分流规则 - 按优先级排序

# 1. AI类网站和服务 (最高优先级)
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/04_ai_websites.list,AI

# 2. 国外常见网站
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/03_foreign_websites.list,PROXY

# 3. 国内网站直连
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/01_domestic_websites.list,DIRECT

# 4. 国内IP直连
RULE-SET,https://raw.githubusercontent.com/your-repo/surge_rules/main/02_domestic_ip.list,DIRECT

# 本地网络直连
DOMAIN-SUFFIX,local,DIRECT
IP-CIDR,***********/16,DIRECT
IP-CIDR,10.0.0.0/8,DIRECT
IP-CIDR,**********/12,DIRECT
IP-CIDR,*********/8,DIRECT

# 最终规则
FINAL,PROXY

[URL Rewrite]
# URL重写规则

[MITM]
# MITM设置
""".format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)

        print(f"📄 Surge配置模板已保存到: {template_file}")
        return template_file


def main():
    parser = argparse.ArgumentParser(
        description="Surge分流规则收集器 - 专门收集四类规则",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
四类规则说明:
  1. 国内网站 - 中国大陆网站域名规则
  2. 国内IP - 中国大陆IP段和ASN规则
  3. 国外常见网站 - 国外主流网站和服务
  4. AI类网站 - AI相关网站和服务

使用示例:
  python surge_rules_collector.py              # 收集所有四类规则
  python surge_rules_collector.py -o my_rules  # 指定输出目录
        """
    )
    parser.add_argument("-o", "--output", default="surge_rules",
                       help="输出目录 (默认: surge_rules)")

    args = parser.parse_args()

    try:
        # 创建收集器
        collector = SurgeRulesCollector(args.output)

        # 收集所有规则
        all_rules = collector.collect_all_rules()

        # 保存分类规则
        category_summary, total_rules = collector.save_all_categories(all_rules)

        # 生成摘要
        summary_file = collector.generate_summary(category_summary, total_rules)

        # 创建配置模板
        template_file = collector.create_surge_config_template()

        # 显示最终结果
        print("\n" + "=" * 60)
        print("🎉 规则收集完成!")
        print("=" * 60)
        print(f"📁 输出目录: {args.output}")
        print(f"📊 总规则数: {total_rules}")
        print(f"📋 摘要文件: {summary_file}")
        print(f"📄 配置模板: {template_file}")
        print("\n生成的文件:")
        for category, info in category_summary.items():
            print(f"  ✅ {info['description']}: {info['count']} 条规则")

        print(f"\n💡 使用建议:")
        print(f"  1. 将规则文件上传到GitHub或其他托管服务")
        print(f"  2. 在Surge中使用RULE-SET引用规则文件")
        print(f"  3. 参考生成的配置模板进行配置")

    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
