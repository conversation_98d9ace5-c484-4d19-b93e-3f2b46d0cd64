#!/usr/bin/env python3
"""
测试Surge规则收集器的基本功能
"""

import os
import sys
import tempfile
import shutil
from surge_rules_collector import SurgeRulesCollector


def test_rule_validation():
    """测试规则验证功能"""
    print("🧪 测试规则验证功能...")
    
    collector = SurgeRulesCollector()
    
    # 测试有效规则
    valid_rules = [
        "DOMAIN-SUFFIX,google.com",
        "DOMAIN,www.baidu.com", 
        "IP-CIDR,***********/24",
        "IP-ASN,4134",
        "DOMAIN-KEYWORD,youtube"
    ]
    
    # 测试无效规则
    invalid_rules = [
        "# 这是注释",
        "",
        "// 另一种注释",
        "INVALID-RULE,test"
    ]
    
    print("  有效规则测试:")
    for rule in valid_rules:
        result = collector.validate_rule_line(rule)
        print(f"    {rule} -> {'✅' if result else '❌'}")
    
    print("  无效规则测试:")
    for rule in invalid_rules:
        result = collector.validate_rule_line(rule)
        print(f"    {rule} -> {'❌' if not result else '⚠️'}")


def test_rule_categorization():
    """测试规则分类功能"""
    print("\n🧪 测试规则分类功能...")
    
    collector = SurgeRulesCollector()
    
    test_rules = [
        ("DOMAIN-SUFFIX,baidu.com", "domestic"),
        ("DOMAIN-SUFFIX,google.com", "foreign"),
        ("DOMAIN-SUFFIX,openai.com", "ai"),
        ("IP-CIDR,***********/24", "ip"),
        ("IP-ASN,4134", "ip"),
        ("DOMAIN-SUFFIX,chatgpt.com", "ai"),
        ("DOMAIN-SUFFIX,qq.com", "domestic")
    ]
    
    for rule, expected in test_rules:
        result = collector.categorize_rule(rule)
        status = "✅" if result == expected else "❌"
        print(f"    {rule} -> {result} (期望: {expected}) {status}")


def test_rule_processing():
    """测试规则处理功能"""
    print("\n🧪 测试规则处理功能...")
    
    collector = SurgeRulesCollector()
    
    # 模拟规则内容
    test_content = """# 测试规则文件
# 这是注释行

DOMAIN-SUFFIX,baidu.com
DOMAIN-SUFFIX,google.com
DOMAIN-SUFFIX,openai.com
IP-CIDR,***********/24
IP-ASN,4134

# 另一个注释
DOMAIN-KEYWORD,youtube
"""
    
    categorized = collector.process_rules_by_category(test_content, "test_rules")
    
    print("  分类结果:")
    for category, rules in categorized.items():
        if rules:
            print(f"    {category}: {len(rules)} 条规则")
            for rule in rules[:3]:  # 只显示前3条
                print(f"      - {rule}")
            if len(rules) > 3:
                print(f"      ... 还有 {len(rules) - 3} 条")


def test_file_operations():
    """测试文件操作功能"""
    print("\n🧪 测试文件操作功能...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="surge_test_")
    print(f"  使用临时目录: {temp_dir}")
    
    try:
        collector = SurgeRulesCollector(temp_dir)
        
        # 测试保存规则
        test_rules = [
            "DOMAIN-SUFFIX,test1.com",
            "DOMAIN-SUFFIX,test2.com",
            "IP-CIDR,10.0.0.0/8"
        ]
        
        count = collector.save_category_rules(test_rules, 'domestic', '测试国内规则')
        print(f"  保存规则: {count} 条")
        
        # 检查文件是否创建
        expected_file = os.path.join(temp_dir, "01_domestic_websites.list")
        if os.path.exists(expected_file):
            print(f"  ✅ 文件创建成功: {expected_file}")
            
            # 读取文件内容验证
            with open(expected_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "DOMAIN-SUFFIX,test1.com" in content:
                    print("  ✅ 文件内容正确")
                else:
                    print("  ❌ 文件内容错误")
        else:
            print("  ❌ 文件创建失败")
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"  🧹 清理临时目录: {temp_dir}")


def main():
    """主测试函数"""
    print("🚀 Surge Rules Collector 功能测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_rule_validation()
        test_rule_categorization()
        test_rule_processing()
        test_file_operations()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        print("✅ 基本功能验证通过")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
